import Stripe from 'stripe';

// Stripe configuration
// const stripeConfig = {
//   apiVersion: '2024-04-10' as const,
// };

// Initialize Stripe instances
export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string);
export const stripeUS = new Stripe(process.env.STRIPE_SECRET_KEY_US as string);

// Helper function to get the appropriate Stripe instance
export function getStripeInstance(isUS: boolean = false): Stripe {
  return isUS ? stripeUS : stripe;
}

// Type definitions for common request bodies
export interface StripeCustomerRequest {
  email: string;
  isUS?: string;
}

export interface StripeUpdateCustomerRequest {
  customerId: string;
  paymentMethodId: string;
  city: string;
  country: string;
  line1: string;
  line2?: string;
  postalCode: string;
  state: string;
  name: string;
  phone: string;
  isUS?: string;
}

export interface StripeSetupIntentRequest {
  customerId: string;
  paymentMethodTypes: string[];
  isUS?: string;
}

export interface StripePaymentMethodRequest {
  paymentMethodId?: string;
  customerId?: string;
  email?: string;
  name?: string;
  phone?: string;
  city?: string;
  country?: string;
  line1?: string;
  line2?: string;
  postalCode?: string;
  state?: string;
  isUS?: string;
}

export interface StripePaymentIntentRequest {
  customerId: string;
  amount: string;
  currency: string;
  receipt_email: string;
  capture_method: string;
  isUS?: string;
}

export interface StripeUpdatePaymentIntentRequest {
  paymentId: string;
  orderId: string;
  description: string;
  receipt_email: string;
  isUS?: string;
}

export interface StripeCapturePaymentRequest {
  paymentId: string;
  isUS?: string;
}

export interface StripeTransferRequest {
  amount: string;
  currency: string;
  chargeId: string;
  accountId: string;
  isUS?: string;
}

export interface StripeUpdateTransferRequest {
  transferId: string;
  orderId: string;
  description: string;
  isUS?: string;
}

export interface StripeUpdateChargeRequest {
  paymentId: string;
  orderId: string;
  description: string;
  accountId: string;
  isUS?: string;
}

export interface StripeCreateAccountRequest {
  email: string;
  isUS?: string;
}

export interface StripeAccountLinkRequest {
  accountId: string;
  refreshUrl: string;
  returnUrl: string;
  type: string;
  isUS?: string;
}

export interface StripeAccountRequest {
  accountId: string;
  isUS?: string;
}

export interface StripeInvoiceRequest {
  customerId: string;
  isUS?: string;
}
