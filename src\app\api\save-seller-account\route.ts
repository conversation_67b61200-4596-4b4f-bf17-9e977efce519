import { NextRequest, NextResponse } from 'next/server';
import { doc, setDoc } from 'firebase/firestore';
import { initFirebase } from '../../../../firebaseConfig';
import { getUserIdFromRequest } from '@/lib/auth/serverAuth';

export async function POST(req: NextRequest) {
  try {
    const { sellerId, account, userId } = await req.json();

    // Determine the user ID to use (priority: sellerId > userId > auth)
    let finalUserId = sellerId || userId;

    if (!finalUserId) {
      finalUserId = await getUserIdFromRequest(req);
    }

    if (!finalUserId || !account) {
      return NextResponse.json(
        { error: 'Missing userId/sellerId or account ID' },
        { status: 400 }
      );
    }

    console.log('Saving seller account:', { userId: finalUserId, account });

    // Initialize Firebase and get Firestore instance
    const { db } = await initFirebase();

    // Save the mapping in Firestore
    const sellerRef = doc(db, 'sellers', finalUserId);
    await setDoc(sellerRef, {
      stripeAccountId: account,
      onboardingComplete: true,
      updatedAt: new Date().toISOString()
    }, { merge: true });

    return NextResponse.json({
      message: 'Seller account saved successfully',
      userId: finalUserId,
      stripeAccountId: account
    });
  } catch (error) {
    console.error('Error saving seller account:', error);
    return NextResponse.json(
      { error: 'Failed to save seller account' },
      { status: 500 }
    );
  }
}
