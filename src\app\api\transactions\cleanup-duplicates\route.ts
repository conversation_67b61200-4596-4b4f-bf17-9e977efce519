import { NextRequest, NextResponse } from 'next/server';
import { findDuplicateTransactions } from '@/services/transactionService';
import { initFirebase } from '../../../../../firebaseConfig';
import { doc, deleteDoc } from 'firebase/firestore';

export async function POST(request: NextRequest) {
  try {
    const { userId, dryRun = true } = await request.json();

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // Find duplicate transactions
    const result = await findDuplicateTransactions(userId);

    if (!result.success) {
      return NextResponse.json({
        success: false,
        error: result.error,
      }, { status: 500 });
    }

    const duplicates = result.duplicates || [];

    if (dryRun) {
      // Just return what would be deleted without actually deleting
      return NextResponse.json({
        success: true,
        message: 'Dry run completed',
        duplicatesFound: duplicates.length,
        duplicates: duplicates.map(d => ({
          id: d.id,
          stripeSessionId: d.stripeSessionId,
          amount: d.amount,
          currency: d.currency,
          createdAt: d.createdAt,
          status: d.status,
        })),
      });
    }

    // Actually delete the duplicates
    const { db } = await initFirebase();
    const deletedIds: string[] = [];

    for (const duplicate of duplicates) {
      try {
        await deleteDoc(doc(db, "transactions", duplicate.id));
        deletedIds.push(duplicate.id);
      } catch (error) {
        console.error(`Error deleting transaction ${duplicate.id}:`, error);
      }
    }

    return NextResponse.json({
      success: true,
      message: `Cleanup completed`,
      duplicatesFound: duplicates.length,
      duplicatesDeleted: deletedIds.length,
      deletedIds,
    });

  } catch (error) {
    console.error('Error cleaning up duplicates:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to cleanup duplicates',
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Transaction cleanup endpoint',
    usage: {
      method: 'POST',
      body: {
        userId: 'string (required)',
        dryRun: 'boolean (optional, default: true)',
      },
      description: 'Set dryRun to false to actually delete duplicates',
    },
  });
}
