'use client';

import { useState } from 'react';

interface ApiEndpoint {
  name: string;
  endpoint: string;
  method: string;
  description: string;
  fields: Array<{
    name: string;
    type: string;
    required: boolean;
    placeholder?: string;
    options?: string[];
  }>;
}

interface EscrowFormData {
  [key: string]: string;
}

const escrowApis: ApiEndpoint[] = [
  {
    name: '🚀 Create Escrow Payment',
    endpoint: '/api/escrow/create',
    method: 'POST',
    description: 'Create escrow payment with automatic capture - Returns checkout URL',
    fields: [
      { name: 'userId', type: 'text', required: true, placeholder: 'buyer_user_123' },
      { name: 'userEmail', type: 'email', required: true, placeholder: '<EMAIL>' },
      { name: 'sellerId', type: 'text', required: true, placeholder: 'seller_user_456' },
      { name: 'sellerEmail', type: 'email', required: true, placeholder: '<EMAIL>' },
      { name: 'sellerStripeAccountId', type: 'text', required: true, placeholder: 'acct_...' },
      { name: 'orderId', type: 'text', required: true, placeholder: 'order_24' },
      { name: 'amount', type: 'number', required: true, placeholder: '10400' },
      { name: 'currency', type: 'text', required: false, placeholder: 'usd' },
      { name: 'productName', type: 'text', required: true, placeholder: 'Service Order' },
      { name: 'productDescription', type: 'text', required: false, placeholder: 'Service description' }
    ]
  },
  {
    name: '💳 Capture Payment (Uncaptured → Succeeded)',
    endpoint: '/api/stripe/capture-payment',
    method: 'POST',
    description: 'Capture payment to change status from Uncaptured to Succeeded in Stripe dashboard',
    fields: [
      { name: 'paymentIntentId', type: 'text', required: false, placeholder: 'pi_3RjmP4E85bVPMZKN0ddAso4T' },
      { name: 'chargeId', type: 'text', required: false, placeholder: 'ch_3RjmP4E85bVPMZKN0m1uUpFF' },
      { name: 'isUS', type: 'select', required: false, options: ['false', 'true'] }
    ]
  },
  {
    name: '✅ Accept Stage (10% Release)',
    endpoint: '/api/escrow/release',
    method: 'POST',
    description: 'Release Accept stage - Transfer 10% of seller amount (8.4% of total)',
    fields: [
      { name: 'orderId', type: 'text', required: true, placeholder: 'order_24' },
      { name: 'stage', type: 'text', required: true, placeholder: 'accept' },
      { name: 'chargeId', type: 'text', required: true, placeholder: 'ch_...' }
    ]
  },
  {
    name: '🚚 Delivered Stage (10% Release)',
    endpoint: '/api/escrow/release',
    method: 'POST',
    description: 'Release Delivered stage - Transfer another 10% of seller amount',
    fields: [
      { name: 'orderId', type: 'text', required: true, placeholder: 'order_24' },
      { name: 'stage', type: 'text', required: true, placeholder: 'delivered' },
      { name: 'chargeId', type: 'text', required: true, placeholder: 'ch_...' }
    ]
  },
  {
    name: '🎉 Completed Stage (80% Release)',
    endpoint: '/api/escrow/release',
    method: 'POST',
    description: 'Release Completed stage - Transfer remaining 80% of seller amount',
    fields: [
      { name: 'orderId', type: 'text', required: true, placeholder: 'order_24' },
      { name: 'stage', type: 'text', required: true, placeholder: 'completed' },
      { name: 'chargeId', type: 'text', required: true, placeholder: 'ch_...' }
    ]
  },
  {
    name: '🔍 Release by Payment Intent',
    endpoint: '/api/escrow/release-by-payment-intent',
    method: 'POST',
    description: 'Release escrow stage using payment intent ID - auto-finds charge ID',
    fields: [
      { name: 'orderId', type: 'text', required: true, placeholder: 'order_24' },
      { name: 'stage', type: 'select', required: true, options: ['accept', 'delivered', 'completed'] },
      { name: 'paymentIntentId', type: 'text', required: true, placeholder: 'pi_...' },
      { name: 'isUS', type: 'select', required: false, options: ['false', 'true'] }
    ]
  },
  {
    name: '💰 Create Refund',
    endpoint: '/api/stripe/refund',
    method: 'POST',
    description: 'Create a full or partial refund for escrow payment',
    fields: [
      { name: 'chargeId', type: 'text', required: false, placeholder: 'ch_...' },
      { name: 'paymentIntentId', type: 'text', required: false, placeholder: 'pi_...' },
      { name: 'amount', type: 'number', required: false, placeholder: '1000 (leave empty for full refund)' },
      { name: 'reason', type: 'select', required: false, options: ['requested_by_customer', 'duplicate', 'fraudulent'] },
      { name: 'reverseTransfer', type: 'select', required: false, options: ['false', 'true'] },
      { name: 'isUS', type: 'select', required: false, options: ['false', 'true'] }
    ]
  },
  {
    name: '📋 List Refunds',
    endpoint: '/api/stripe/refunds/list',
    method: 'POST',
    description: 'List all refunds for escrow payment with summary',
    fields: [
      { name: 'chargeId', type: 'text', required: false, placeholder: 'ch_...' },
      { name: 'paymentIntentId', type: 'text', required: false, placeholder: 'pi_...' },
      { name: 'limit', type: 'number', required: false, placeholder: '10' },
      { name: 'isUS', type: 'select', required: false, options: ['false', 'true'] }
    ]
  }
];

export default function EscrowPage() {
  const [selectedApi, setSelectedApi] = useState<ApiEndpoint>(escrowApis[0]);
  const [formData, setFormData] = useState<EscrowFormData>({});
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const handleInputChange = (field: keyof EscrowFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleReleaseByPaymentIntent = async () => {
    if (!formData.orderId || !formData.stage || !formData.paymentIntentId) {
      setError('Order ID, stage, and payment intent ID are required');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('/api/escrow/release-by-payment-intent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          orderId: formData.orderId,
          stage: formData.stage,
          paymentIntentId: formData.paymentIntentId,
          isUS: formData.isUS
        })
      });

      const data = await response.json();
      
      if (response.ok) {
        setResult(data);
      } else {
        setError(data.error || 'Failed to release escrow stage');
      }
    } catch (err) {
      setError('Network error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleReleaseByChargeId = async () => {
    if (!formData.orderId || !formData.stage || !formData.chargeId) {
      setError('Order ID, stage, and charge ID are required');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('/api/escrow/release', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          orderId: formData.orderId,
          stage: formData.stage,
          chargeId: formData.chargeId
        })
      });

      const data = await response.json();
      
      if (response.ok) {
        setResult(data);
      } else {
        setError(data.error || 'Failed to release escrow stage');
      }
    } catch (err) {
      setError('Network error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-8 flex items-center">
            <svg className="w-8 h-8 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
            Escrow Management
          </h1>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Release by Payment Intent */}
            <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
              <h2 className="text-xl font-semibold text-blue-800 mb-4 flex items-center">
                <svg className="w-6 h-6 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                Release by Payment Intent
              </h2>
              <p className="text-sm text-blue-700 mb-4">Auto-finds charge ID from payment intent</p>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Order ID</label>
                  <input
                    type="text"
                    value={formData.orderId}
                    onChange={(e) => handleInputChange('orderId', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="order_21"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Escrow Stage</label>
                  <select
                    value={formData.stage}
                    onChange={(e) => handleInputChange('stage', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="accept">Accept (10%)</option>
                    <option value="delivered">Delivered (10%)</option>
                    <option value="completed">Completed (80%)</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Payment Intent ID</label>
                  <input
                    type="text"
                    value={formData.paymentIntentId}
                    onChange={(e) => handleInputChange('paymentIntentId', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="pi_..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">US Stripe</label>
                  <select
                    value={formData.isUS}
                    onChange={(e) => handleInputChange('isUS', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="false">No</option>
                    <option value="true">Yes</option>
                  </select>
                </div>

                <button
                  onClick={handleReleaseByPaymentIntent}
                  disabled={loading}
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                >
                  {loading ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Processing...
                    </>
                  ) : (
                    'Release Escrow Stage'
                  )}
                </button>
              </div>
            </div>

            {/* Release by Charge ID */}
            <div className="bg-green-50 rounded-lg p-6 border border-green-200">
              <h2 className="text-xl font-semibold text-green-800 mb-4 flex items-center">
                <svg className="w-6 h-6 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Release by Charge ID
              </h2>
              <p className="text-sm text-green-700 mb-4">Direct release using charge ID</p>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Order ID</label>
                  <input
                    type="text"
                    value={formData.orderId}
                    onChange={(e) => handleInputChange('orderId', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    placeholder="order_21"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Escrow Stage</label>
                  <select
                    value={formData.stage}
                    onChange={(e) => handleInputChange('stage', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  >
                    <option value="accept">Accept (10%)</option>
                    <option value="delivered">Delivered (10%)</option>
                    <option value="completed">Completed (80%)</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Charge ID</label>
                  <input
                    type="text"
                    value={formData.chargeId}
                    onChange={(e) => handleInputChange('chargeId', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    placeholder="ch_..."
                  />
                </div>

                <button
                  onClick={handleReleaseByChargeId}
                  disabled={loading}
                  className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                >
                  {loading ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Processing...
                    </>
                  ) : (
                    'Release Escrow Stage'
                  )}
                </button>
              </div>
            </div>
          </div>

          {/* Results Section */}
          {(result || error) && (
            <div className="mt-8">
              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                  <div className="flex">
                    <svg className="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <div>
                      <h3 className="text-sm font-medium text-red-800">Error</h3>
                      <p className="text-sm text-red-700 mt-1">{error}</p>
                    </div>
                  </div>
                </div>
              )}

              {result && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-green-800 mb-4 flex items-center">
                    <svg className="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Escrow Release Successful
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div className="bg-white p-4 rounded-lg border">
                      <label className="block text-sm font-medium text-gray-700 mb-1">Order ID</label>
                      <p className="text-sm text-gray-900 font-mono">{result.orderId}</p>
                    </div>
                    
                    <div className="bg-white p-4 rounded-lg border">
                      <label className="block text-sm font-medium text-gray-700 mb-1">Stage</label>
                      <p className="text-sm text-gray-900 font-semibold">{result.stage}</p>
                    </div>
                    
                    {result.escrowRelease && (
                      <div className="bg-white p-4 rounded-lg border">
                        <label className="block text-sm font-medium text-gray-700 mb-1">Amount Released</label>
                        <p className="text-sm text-gray-900 font-bold text-green-600">
                          {result.escrowRelease.amountFormatted} ({result.escrowRelease.percentage}%)
                        </p>
                      </div>
                    )}
                    
                    {result.chargeId && (
                      <div className="bg-white p-4 rounded-lg border">
                        <label className="block text-sm font-medium text-gray-700 mb-1">Charge ID</label>
                        <p className="text-xs text-gray-900 font-mono break-all">{result.chargeId}</p>
                      </div>
                    )}
                    
                    {result.transfer && (
                      <div className="bg-white p-4 rounded-lg border">
                        <label className="block text-sm font-medium text-gray-700 mb-1">Transfer Amount</label>
                        <p className="text-sm text-gray-900 font-bold text-blue-600">{result.transfer.amountFormatted}</p>
                      </div>
                    )}
                  </div>

                  <div className="mt-4">
                    <details className="bg-white rounded-lg border p-4">
                      <summary className="cursor-pointer text-sm font-medium text-gray-700">View Full Response</summary>
                      <pre className="mt-2 text-xs text-gray-600 overflow-auto">
                        {JSON.stringify(result, null, 2)}
                      </pre>
                    </details>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
