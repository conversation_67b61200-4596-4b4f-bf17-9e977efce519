import { useState } from 'react';
import { useCurrentUser } from '@/hooks/useCurrentUser';

export default function SellerOnboard() {
  const [loading, setLoading] = useState(false);
  const { user, loading: userLoading, isAuthenticated } = useCurrentUser();

  const handleOnboard = async () => {
    if (!user?.uid) {
      alert('Please log in first to become a seller');
      return;
    }

    setLoading(true);
    try {
      const res = await fetch('/api/connect/onboard', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: user.uid })
      });

      const data = await res.json();

      if (data.error) {
        alert(data.error);
        setLoading(false);
        return;
      }

      window.location.href = data.url; // redirect seller to Stripe onboarding
    } catch (error) {
      console.error('Error starting onboarding:', error);
      alert('Failed to start onboarding process');
      setLoading(false);
    }
  };

  if (userLoading) {
    return (
      <div>
        <h1>Become a Seller</h1>
        <p>Loading...</p>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div>
        <h1>Become a Seller</h1>
        <p>Please log in to start the seller onboarding process.</p>
      </div>
    );
  }

  return (
    <div>
      <h1>Become a Seller</h1>
      <p>User ID: {user.uid}</p>
      <p>Email: {user.email}</p>
      <button onClick={handleOnboard} disabled={loading}>
        {loading ? 'Redirecting...' : 'Connect with Stripe'}
      </button>
    </div>
  );
}
