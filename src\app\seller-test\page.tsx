"use client";

import { useState, useEffect } from 'react';
import { useCurrentUser } from '@/hooks/useCurrentUser';

export default function SellerTestPage() {
  const { user, loading: userLoading, isAuthenticated } = useCurrentUser();
  const [sellerInfo, setSellerInfo] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchSellerInfo = async () => {
    if (!user?.uid) return;

    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/sellers/${user.uid}`);
      const data = await response.json();
      
      if (data.success) {
        setSellerInfo(data.seller);
      } else {
        setError(data.error);
      }
    } catch (err) {
      setError('Failed to fetch seller information');
      console.error('Error fetching seller info:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user?.uid) {
      fetchSellerInfo();
    }
  }, [user?.uid]);

  const testEscrowCreate = async () => {
    if (!user?.uid) {
      alert('Please log in first');
      return;
    }

    if (!sellerInfo?.stripeAccountId) {
      alert('Please complete seller onboarding first');
      return;
    }

    try {
      const response = await fetch('/api/escrow/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.uid,
          userEmail: user.email,
          sellerId: user.uid, // Using same user as seller for testing
          orderId: `test_order_${Date.now()}`,
          amount: 1234, // $12.34
          currency: 'gbp',
          productName: 'Test Product',
          productDescription: 'Test escrow transaction with dynamic seller'
        })
      });

      const data = await response.json();
      
      if (data.success) {
        alert(`Escrow created successfully! Session URL: ${data.sessionUrl}`);
        // Optionally redirect to checkout
        // window.location.href = data.sessionUrl;
      } else {
        alert(`Error: ${data.error}`);
      }
    } catch (error) {
      console.error('Error creating escrow:', error);
      alert('Failed to create escrow transaction');
    }
  };

  if (userLoading) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">Seller Test Page</h1>
        <p>Loading user information...</p>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">Seller Test Page</h1>
        <p>Please log in to test the seller functionality.</p>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Dynamic Seller System Test</h1>
      
      {/* User Info */}
      <div className="bg-blue-50 p-4 rounded-lg mb-6">
        <h2 className="text-lg font-semibold mb-2">Current User</h2>
        <p><strong>User ID:</strong> {user.uid}</p>
        <p><strong>Email:</strong> {user.email}</p>
        <p><strong>Email Verified:</strong> {user.emailVerified ? 'Yes' : 'No'}</p>
      </div>

      {/* Seller Info */}
      <div className="bg-green-50 p-4 rounded-lg mb-6">
        <h2 className="text-lg font-semibold mb-2">Seller Information</h2>
        
        {loading && <p>Loading seller info...</p>}
        
        {error && (
          <div className="text-red-600 mb-4">
            <p><strong>Error:</strong> {error}</p>
            <p className="text-sm mt-2">This means you haven't completed seller onboarding yet.</p>
          </div>
        )}
        
        {sellerInfo && (
          <div>
            <p><strong>Stripe Account ID:</strong> {sellerInfo.stripeAccountId}</p>
            <p><strong>Onboarding Complete:</strong> {sellerInfo.onboardingComplete ? 'Yes' : 'No'}</p>
            <p><strong>Created At:</strong> {sellerInfo.createdAt}</p>
            {sellerInfo.userEmail && <p><strong>Email:</strong> {sellerInfo.userEmail}</p>}
            {sellerInfo.userName && <p><strong>Name:</strong> {sellerInfo.userName}</p>}
          </div>
        )}
        
        <button 
          onClick={fetchSellerInfo}
          className="mt-4 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
        >
          Refresh Seller Info
        </button>
      </div>

      {/* Actions */}
      <div className="bg-gray-50 p-4 rounded-lg mb-6">
        <h2 className="text-lg font-semibold mb-4">Actions</h2>
        
        <div className="space-y-4">
          {!sellerInfo && (
            <div>
              <p className="mb-2">Step 1: Become a seller</p>
              <a 
                href="/payment" 
                className="inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Go to Seller Onboarding
              </a>
            </div>
          )}
          
          {sellerInfo && (
            <div>
              <p className="mb-2">Step 2: Test escrow creation with dynamic seller resolution</p>
              <button 
                onClick={testEscrowCreate}
                className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
              >
                Create Test Escrow Transaction
              </button>
              <p className="text-sm text-gray-600 mt-2">
                This will create an escrow transaction using your user ID as both buyer and seller (for testing).
                The system will automatically resolve your seller information.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* API Endpoints */}
      <div className="bg-yellow-50 p-4 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">Available API Endpoints</h2>
        <ul className="space-y-1 text-sm">
          <li><code>GET /api/sellers/{user.uid}</code> - Get seller info for current user</li>
          <li><code>GET /api/sellers</code> - Get all sellers (admin)</li>
          <li><code>POST /api/connect/onboard</code> - Start seller onboarding (now dynamic)</li>
          <li><code>POST /api/save-seller-account</code> - Save seller account (now dynamic)</li>
          <li><code>POST /api/escrow/create</code> - Create escrow with auto seller resolution</li>
        </ul>
      </div>
    </div>
  );
}
