import React, { useState } from "react";
import { CheckSquare, Square } from "react-feather";
import { <PERSON><PERSON>, <PERSON>dalContent, ModalBody } from "@heroui/react";
import { But<PERSON> } from "../ui/button";
import { updateOrder } from '../../services/ordersServices';

interface BasketItem {
  id: number;
  orderId?: string; // Original Firebase document ID for updates
  title: string;
  time: string;
  subtotal: number;
  image: string;
  userName: string;
}

interface ConfirmPaymentProps {
  selectedItem: BasketItem | null;
  onConfirm: () => void;
  currencySymbol?: string; // Added currency symbol prop
}

const ConfirmPayment: React.FC<ConfirmPaymentProps> = ({
  selectedItem,
  onConfirm,
  currencySymbol = "$", // Default to $ if not provided
}) => {
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [checked, setChecked] = useState(false);

  if (!selectedItem) return null;

  const transactionFee = selectedItem.subtotal * 0.04;
  const total = selectedItem.subtotal + transactionFee;

  const handlePaymentConfirm = async () => {
    if (selectedItem?.orderId || selectedItem?.id) {
      await updateOrder(selectedItem.orderId || selectedItem.id.toString(), { status: 'NEW' });
    }
    setIsPaymentModalOpen(false);
    onConfirm();
  };

  return (
    <div className="flex flex-col mr-4">
      {/* <div className="flex items-center gap-4 mb-6 border-b pb-4">
        <img src={selectedItem.image} alt="" className="w-12 h-12 rounded-full" />
        <div>
          <p className="text-lg font-semibold">{selectedItem.userName}</p>
          <p className="text-primary font-bold">{selectedItem.title}</p>
        </div>
      </div> */}

      <div className="flex justify-between">
        <p className="text-[#404040]">Subtotal</p>
        <p className="font-bold text-primary">
          {currencySymbol}
          {selectedItem.subtotal.toFixed(2)}
        </p>
      </div>
      <div className="flex justify-between my-1">
        <p className="text-[#404040]">Transaction fee (4%)</p>
        <p className="font-bold text-primary">
          {currencySymbol}
          {transactionFee.toFixed(2)}
        </p>
      </div>
      <div className="flex justify-between mb-4">
        <p className="text-[#404040]">Order total</p>
        <p className="font-bold text-primary">
          {currencySymbol}
          {total.toFixed(2)}
        </p>
      </div>

      <div className="flex justify-between text-subtitle mb-2">
        <p>Delivery time</p>
        <p className="font-semibold">{selectedItem.time}</p>
      </div>

      <div className="flex flex-row gap-2 mt-2 border-b-2 pb-3">
        <div onClick={() => setChecked((prev) => !prev)} className="cursor-pointer select-none">
          {checked ? <CheckSquare color="#333333" /> : <Square color="#bdbdbd" />}
        </div>
        <div>
          <p className="text-primary">Request an invoice </p>
          <p className="text-[#898887]">
            Note: to obtain an Invoice you'll need to provide your tax details (legal name, address
            and VAT registration number).
          </p>
        </div>
      </div>
      <div className="mt-3">
        <p className="text-subtitle">
          Terms: By placing your order, you confirm that you agree to the User Terms and Conditions.
        </p>
        <button
          onClick={() => setIsPaymentModalOpen(true)}
          className="btn-xs text-white btn py-4 w-full bg-primary rounded-full mt-2"
        >
          Confirm Payment
        </button>
      </div>

      <Modal
        placement="auto"
        isOpen={isPaymentModalOpen}
        onClose={() => setIsPaymentModalOpen(false)}
        hideCloseButton={true}
      >
        <ModalContent className="modal-content w-80 p-12 rounded-3xl">
          {() => (
            <>
              <ModalBody>
                <p className="text-center text-black text-lg">
                  Confirm payment of {currencySymbol}
                  {total.toFixed(2)}?
                </p>
                <div>
                  <Button
                    variant="outline"
                    className="rounded-full w-full mt-5 border-black text-black border-2 py-5 text-base"
                    onClick={handlePaymentConfirm}
                  >
                    Yes, proceed with payment
                  </Button>
                  <Button
                    variant="outline"
                    className="rounded-full w-full mt-3 border-black text-black border-2 py-5 text-base"
                    onClick={() => setIsPaymentModalOpen(false)}
                  >
                    No, cancel
                  </Button>
                </div>
              </ModalBody>
            </>
          )}
        </ModalContent>
      </Modal>
    </div>
  );
};

export default ConfirmPayment;
