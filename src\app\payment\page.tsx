"use client";

import { useState, useEffect } from 'react';
import { initFirebase } from '../../../firebaseConfig';
import { getAuth } from 'firebase/auth';

interface ApiResponse {
  success: boolean;
  error?: string;
  [key: string]: any;
}

interface ApiField {
  name: string;
  type: 'text' | 'email' | 'number' | 'select' | 'textarea';
  required: boolean;
  placeholder?: string;
  options?: string[];
}

interface ApiEndpoint {
  name: string;
  endpoint: string;
  method: string;
  description: string;
  fields: ApiField[];
}

export default function StripeApiTester() {
  const [loading, setLoading] = useState<string | null>(null);
  const [results, setResults] = useState<Record<string, ApiResponse>>({});
  const [activeCategory, setActiveCategory] = useState<string>('checkout');
  const [formData, setFormData] = useState<Record<string, Record<string, string>>>({});

  const handleInputChange = (endpoint: string, field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [endpoint]: {
        ...prev[endpoint],
        [field]: value
      }
    }));
  };

  const testApi = async (apiEndpoint: ApiEndpoint) => {
    setLoading(apiEndpoint.endpoint);
    try {
      const data = formData[apiEndpoint.endpoint] || {};

      // Convert string values to appropriate types
      const processedData: any = {};
      apiEndpoint.fields.forEach(field => {
        const value = data[field.name];
        if (value !== undefined && value !== '') {
          if (field.type === 'number') {
            processedData[field.name] = parseInt(value);
          } else if (field.name === 'paymentMethodTypes') {
            processedData[field.name] = value.split(',').map(s => s.trim());
          } else {
            processedData[field.name] = value;
          }
        }
      });

      let url = apiEndpoint.endpoint;
      let fetchOptions: RequestInit = {
        method: apiEndpoint.method,
        headers: { 'Content-Type': 'application/json' },
      };

      // Handle path parameters (e.g., {transactionId}, {sessionId})
      Object.entries(processedData).forEach(([key, value]) => {
        if (value !== undefined && value !== '') {
          url = url.replace(`{${key}}`, String(value));
        }
      });

      if (apiEndpoint.method === 'GET') {
        // For GET requests, add remaining data as query parameters
        const queryParams = new URLSearchParams();
        Object.entries(processedData).forEach(([key, value]) => {
          // Skip if this was used as a path parameter
          if (!apiEndpoint.endpoint.includes(`{${key}}`) && value !== undefined && value !== '') {
            queryParams.append(key, String(value));
          }
        });
        if (queryParams.toString()) {
          url += '?' + queryParams.toString();
        }
      } else {
        // For POST/PUT/etc, send data in body (excluding path parameters)
        const bodyData: any = {};
        Object.entries(processedData).forEach(([key, value]) => {
          if (!apiEndpoint.endpoint.includes(`{${key}}`)) {
            bodyData[key] = value;
          }
        });
        fetchOptions.body = JSON.stringify(bodyData);
      }

      const response = await fetch(url, fetchOptions);

      const responseData = await response.json();

      // Auto-fetch charge ID if we got a transaction ID
      let enhancedResponseData = responseData;
      if (responseData.success && responseData.transactionId && !responseData.chargeId) {
        try {
          console.log('Auto-fetching charge ID for transaction:', responseData.transactionId);
          const transactionResponse = await fetch(`/api/transactions/${responseData.transactionId}`);
          if (transactionResponse.ok) {
            const transactionData = await transactionResponse.json();
            if (transactionData.success && transactionData.paymentIntent?.latest_charge) {
              enhancedResponseData = {
                ...responseData,
                autoFetchedChargeId: transactionData.paymentIntent.latest_charge,
                paymentIntentId: transactionData.paymentIntent.id,
                paymentIntentStatus: transactionData.paymentIntent.status
              };
              console.log('✅ Auto-fetched charge ID:', transactionData.paymentIntent.latest_charge);
            }
          }
        } catch (error) {
          console.log('Could not auto-fetch charge ID:', error);
        }
      }

      setResults(prev => ({ ...prev, [apiEndpoint.endpoint]: enhancedResponseData }));
    } catch (error) {
      setResults(prev => ({
        ...prev,
        [apiEndpoint.endpoint]: {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      }));
    } finally {
      setLoading(null);
    }
  };

  const apiEndpoints: Record<string, ApiEndpoint[]> = {
    // checkout: [
    //   {
    //     name: 'Enhanced Checkout Session',
    //     endpoint: '/api/checkout',
    //     method: 'POST',
    //     description: 'Create a Stripe checkout session with user tracking',
    //     fields: [
    //       { name: 'userId', type: 'text', required: true, placeholder: 'user_123' },
    //       { name: 'userEmail', type: 'email', required: true, placeholder: '<EMAIL>' },
    //       { name: 'amount', type: 'number', required: false, placeholder: '1000' },
    //       { name: 'currency', type: 'text', required: false, placeholder: 'usd' },
    //       { name: 'productName', type: 'text', required: false, placeholder: 'Demo Product' },
    //       { name: 'productDescription', type: 'text', required: false, placeholder: 'Product description' }
    //     ]
    //   },
      // {
      //   name: 'Escrow Checkout Session',
      //   endpoint: '/api/checkout-escrow',
      //   method: 'POST',
      //   description: 'Create an escrow payment with multi-stage releases (10/10/80%)',
      //   fields: [
      //     { name: 'userId', type: 'text', required: true, placeholder: 'buyer_user_123' },
      //     { name: 'userEmail', type: 'email', required: true, placeholder: '<EMAIL>' },
      //     { name: 'sellerId', type: 'text', required: true, placeholder: 'seller_user_456' },
      //     { name: 'sellerEmail', type: 'email', required: true, placeholder: '<EMAIL>' },
      //     { name: 'sellerStripeAccountId', type: 'text', required: true, placeholder: 'acct_...' },
      //     { name: 'orderId', type: 'text', required: true, placeholder: 'order_789' },
      //     { name: 'amount', type: 'number', required: true, placeholder: '10400' },
      //     { name: 'currency', type: 'text', required: false, placeholder: 'usd' },
      //     { name: 'productName', type: 'text', required: false, placeholder: 'Service Order' },
      //     { name: 'productDescription', type: 'text', required: false, placeholder: 'Service description' },
      //     { name: 'isEscrow', type: 'select', required: true, options: ['true'] }
      //   ]
      // }
    // ],
    escrow: [
      {
        name: 'Create Escrow Payment',
        endpoint: '/api/escrow/create',
        method: 'POST',
        description: 'Create an escrow payment directly (alternative to checkout)',
        fields: [
          { name: 'userId', type: 'text', required: true, placeholder: 'buyer_user_123' },
          { name: 'userEmail', type: 'email', required: true, placeholder: '<EMAIL>' },
          { name: 'sellerId', type: 'text', required: true, placeholder: 'seller_user_456' },
          { name: 'sellerEmail', type: 'email', required: true, placeholder: '<EMAIL>' },
          { name: 'sellerStripeAccountId', type: 'text', required: true, placeholder: 'acct_...' },
          { name: 'orderId', type: 'text', required: true, placeholder: 'order_789' },
          { name: 'amount', type: 'number', required: true, placeholder: '10400' },
          { name: 'currency', type: 'text', required: false, placeholder: 'usd' },
          { name: 'productName', type: 'text', required: true, placeholder: 'Service Order' },
          { name: 'productDescription', type: 'text', required: false, placeholder: 'Service description' }
        ]
      },
      {
        name: 'Release Escrow Stage',
        endpoint: '/api/escrow/release',
        method: 'POST',
        description: 'Manually release an escrow stage (10%, 10%, or 80%)',
        fields: [
          { name: 'orderId', type: 'text', required: true, placeholder: 'order_21' },
          { name: 'stage', type: 'select', required: true, options: ['accept', 'delivered', 'completed'] },
          { name: 'chargeId', type: 'text', required: true, placeholder: 'ch_...' }
        ]
      },
      {
        name: 'Release Escrow Stage (Payment Intent)',
        endpoint: '/api/escrow/release-by-payment-intent',
        method: 'POST',
        description: 'Release escrow stage using payment intent ID - auto-finds charge ID',
        fields: [
          { name: 'orderId', type: 'text', required: true, placeholder: 'order_21' },
          { name: 'stage', type: 'select', required: true, options: ['accept', 'delivered', 'completed'] },
          { name: 'paymentIntentId', type: 'text', required: true, placeholder: 'pi_...' },
          { name: 'isUS', type: 'select', required: false, options: ['false', 'true'] }
        ]
      },
      {
        name: 'Auto Release Escrow',
        endpoint: '/api/escrow/auto-release',
        method: 'POST',
        description: 'Automatically release escrow based on order status change',
        fields: [
          { name: 'orderId', type: 'text', required: true, placeholder: 'order_789' },
          { name: 'newStatus', type: 'select', required: true, options: ['accept', 'delivered', 'completed'] },
          { name: 'chargeId', type: 'text', required: true, placeholder: 'ch_...' }
        ]
      },
      {
        name: 'Manage Escrow State',
        endpoint: '/api/escrow/manage-state',
        method: 'POST',
        description: 'Advanced escrow state management with automatic capture and transfer',
        fields: [
          { name: 'orderId', type: 'text', required: true, placeholder: 'order_15' },
          { name: 'chargeId', type: 'text', required: true, placeholder: 'ch_3RjhR4E85bVPMZKN0LHt2Xha' },
          { name: 'newState', type: 'select', required: true, options: ['accept', 'delivered', 'completed'] }
        ]
      }
    ],
    charges: [
      {
        name: 'Get Latest Charge Data',
        endpoint: '/api/charges/latest',
        method: 'GET',
        description: 'Fetch the latest charge data directly from Stripe using Payment Intent ID',
        fields: [
          { name: 'payment_intent_id', type: 'text', required: true, placeholder: 'pi_3RjhR4E85bVPMZKN0dIEcyFL' },
          { name: 'isUS', type: 'select', required: false, options: ['false', 'true'] }
        ]
      },
      {
        name: 'Refresh Charge Data (POST)',
        endpoint: '/api/charges/latest',
        method: 'POST',
        description: 'Refresh charge data using POST method - Firebase Functions style',
        fields: [
          { name: 'paymentIntentId', type: 'text', required: true, placeholder: 'pi_3RjhR4E85bVPMZKN0dIEcyFL' },
          { name: 'isUS', type: 'select', required: false, options: ['false', 'true'] }
        ]
      },
      {
        name: 'List All Charges (Firebase Style)',
        endpoint: '/api/stripe/list-charges',
        method: 'POST',
        description: 'List all charges for payment intent - Following Firebase Functions pattern exactly',
        fields: [
          { name: 'paymentIntentId', type: 'text', required: true, placeholder: 'pi_3RjmP4E85bVPMZKN0ddAso4T' },
          { name: 'isUS', type: 'select', required: false, options: ['false', 'true'] }
        ]
      },
      {
        name: '🎯 Capture Payment (Uncaptured → Succeeded)',
        endpoint: '/api/stripe/capture-payment',
        method: 'POST',
        description: 'Capture payment to change status from Uncaptured to Succeeded in Stripe dashboard',
        fields: [
          { name: 'paymentIntentId', type: 'text', required: false, placeholder: 'pi_3RjmP4E85bVPMZKN0ddAso4T' },
          { name: 'chargeId', type: 'text', required: false, placeholder: 'ch_3RjmP4E85bVPMZKN0m1uUpFF' },
          { name: 'isUS', type: 'select', required: false, options: ['false', 'true'] }
        ]
      },
      {
        name: '💰 Create Refund',
        endpoint: '/api/stripe/refund',
        method: 'POST',
        description: 'Create a full or partial refund for a charge or payment intent',
        fields: [
          { name: 'chargeId', type: 'text', required: false, placeholder: 'ch_3RjmP4E85bVPMZKN0m1uUpFF' },
          { name: 'paymentIntentId', type: 'text', required: false, placeholder: 'pi_3RjmP4E85bVPMZKN0ddAso4T' },
          { name: 'amount', type: 'number', required: false, placeholder: '1000 (leave empty for full refund)' },
          { name: 'reason', type: 'select', required: false, options: ['requested_by_customer', 'duplicate', 'fraudulent'] },
          { name: 'refundApplicationFee', type: 'select', required: false, options: ['false', 'true'] },
          { name: 'reverseTransfer', type: 'select', required: false, options: ['false', 'true'] },
          { name: 'isUS', type: 'select', required: false, options: ['false', 'true'] }
        ]
      },
      {
        name: '📋 List Refunds',
        endpoint: '/api/stripe/refunds/list',
        method: 'POST',
        description: 'List all refunds for a charge or payment intent with summary',
        fields: [
          { name: 'chargeId', type: 'text', required: false, placeholder: 'ch_3RjmP4E85bVPMZKN0m1uUpFF' },
          { name: 'paymentIntentId', type: 'text', required: false, placeholder: 'pi_3RjmP4E85bVPMZKN0ddAso4T' },
          { name: 'limit', type: 'number', required: false, placeholder: '10' },
          { name: 'isUS', type: 'select', required: false, options: ['false', 'true'] }
        ]
      }
    ],
    transaction: [
      {
        name: 'Get Transaction by ID',
        endpoint: '/api/transactions/{transactionId}',
        method: 'GET',
        description: 'Get transaction details including Payment Intent ID',
        fields: [
          { name: 'transactionId', type: 'text', required: true, placeholder: 'pYnAssf4p2DckHaoBTl0' }
        ]
      },
      {
        name: 'Get Transaction by Session ID',
        endpoint: '/api/transactions/session/{sessionId}',
        method: 'GET',
        description: 'Get transaction details using Stripe session ID',
        fields: [
          { name: 'sessionId', type: 'text', required: true, placeholder: 'cs_test_a1B0eClnuocusHiJW74FulaOpsCDBKHtLRlTUOS6fl0FvF6QoYAjJ4tqaS' }
        ]
      },
      {
        name: '🧾 Generate Invoice from Transaction',
        endpoint: '/api/transactions/{transactionId}/invoice',
        method: 'GET',
        description: 'Auto-generate complete invoice with payment intent, charge ID, and money breakdown',
        fields: [
          { name: 'transactionId', type: 'text', required: true, placeholder: 'EmNgsYGATgJ0pOo4JC25' }
        ]
      }
    ],
    // customer: [
    //   {
    //     name: 'Retrieve/Create Customer',
    //     endpoint: '/api/stripe/customer/retrieve',
    //     method: 'POST',
    //     description: 'Get existing customer or create new one by email/userId (Firebase UID)',
    //     fields: [
    //       { name: 'email', type: 'email', required: false, placeholder: '<EMAIL>' },
    //       { name: 'userId', type: 'text', required: false, placeholder: 'Firebase UID (will be used as Stripe customer ID)' },
    //       { name: 'isUS', type: 'select', required: false, options: ['false', 'true'] }
    //     ]
    //   },
      
    // ],
    // account: [
 
    //   {
    //     name: 'Retrieve Account',
    //     endpoint: '/api/stripe/account/retrieve',
    //     method: 'POST',
    //     description: 'Get Stripe account details by ID',
    //     fields: [
    //       { name: 'accountId', type: 'text', required: true, placeholder: 'acct_...' },
    //       { name: 'isUS', type: 'select', required: false, options: ['false', 'true'] }
    //     ]
    //   },
    // ],
   

    // transfer: [
    //   {
    //     name: 'Create Transfer',
    //     endpoint: '/api/stripe/transfer/create',
    //     method: 'POST',
    //     description: 'Transfer funds to connected account',
    //     fields: [
    //       { name: 'amount', type: 'number', required: true, placeholder: '500' },
    //       { name: 'currency', type: 'text', required: true, placeholder: 'usd' },
    //       { name: 'chargeId', type: 'text', required: true, placeholder: 'ch_...' },
    //       { name: 'accountId', type: 'text', required: true, placeholder: 'acct_...' },
    //       { name: 'isUS', type: 'select', required: false, options: ['false', 'true'] }
    //     ]
    //   },
    //   {
    //     name: 'Update Transfer',
    //     endpoint: '/api/stripe/transfer/update',
    //     method: 'POST',
    //     description: 'Update transfer metadata',
    //     fields: [
    //       { name: 'transferId', type: 'text', required: true, placeholder: 'tr_...' },
    //       { name: 'orderId', type: 'text', required: false, placeholder: 'order_19' },
    //       { name: 'description', type: 'text', required: false, placeholder: 'Escrow transfer for order_19' },
    //       { name: 'isUS', type: 'select', required: false, options: ['false', 'true'] }
    //     ]
    //   },
    //   {
    //     name: 'List Transfers',
    //     endpoint: '/api/stripe/transfer/list',
    //     method: 'GET',
    //     description: 'List all transfers for account',
    //     fields: [
    //       { name: 'limit', type: 'number', required: false, placeholder: '10' },
    //       { name: 'destination', type: 'text', required: false, placeholder: 'acct_...' },
    //       { name: 'isUS', type: 'select', required: false, options: ['false', 'true'] }
    //     ]
    //   },
    //   {
    //     name: 'Get Transfer Details',
    //     endpoint: '/api/stripe/transfer/retrieve',
    //     method: 'GET',
    //     description: 'Get details of a specific transfer',
    //     fields: [
    //       { name: 'transferId', type: 'text', required: true, placeholder: 'tr_...' },
    //       { name: 'isUS', type: 'select', required: false, options: ['false', 'true'] }
    //     ]
    //   }
    // ],

    invoice: [
      {
        name: 'Create Invoice',
        endpoint: '/api/stripe/invoice/create',
        method: 'POST',
        description: 'Create an invoice for customer',
        fields: [
          { name: 'customerId', type: 'text', required: true, placeholder: 'cus_Sf4EqJYgJfDN1g' },
          { name: 'isUS', type: 'select', required: false, options: ['false', 'true'] }
        ]
      },
      {
        name: 'Create Invoice with Payment Intent',
        endpoint: '/api/stripe/invoice/create-with-payment',
        method: 'POST',
        description: 'Create invoice and link it to a payment intent for escrow tracking',
        fields: [
          { name: 'customerId', type: 'text', required: true, placeholder: 'cus_Sf4EqJYgJfDN1g' },
          { name: 'paymentIntentId', type: 'text', required: true, placeholder: 'pi_3RjhR4E85bVPMZKN0dIEcyFL' },
          { name: 'amount', type: 'number', required: true, placeholder: '10400' },
          { name: 'currency', type: 'text', required: false, placeholder: 'usd' },
          { name: 'description', type: 'text', required: false, placeholder: 'Escrow payment for order_19' },
          { name: 'orderId', type: 'text', required: false, placeholder: 'order_19' },
          { name: 'metadata', type: 'text', required: false, placeholder: '{"escrow": "true", "stage": "created"}' },
          { name: 'isUS', type: 'select', required: false, options: ['false', 'true'] }
        ]
      },
      {
        name: 'Update Invoice with Payment Intent',
        endpoint: '/api/stripe/invoice/update',
        method: 'POST',
        description: 'Update existing invoice with payment intent ID and escrow details',
        fields: [
          { name: 'invoiceId', type: 'text', required: true, placeholder: 'in_...' },
          { name: 'paymentIntentId', type: 'text', required: true, placeholder: 'pi_3RjhR4E85bVPMZKN0dIEcyFL' },
          { name: 'chargeId', type: 'text', required: false, placeholder: 'ch_3RjhR4E85bVPMZKN0LHt2Xha' },
          { name: 'description', type: 'text', required: false, placeholder: 'Updated with escrow payment details' },
          { name: 'metadata', type: 'text', required: false, placeholder: '{"paymentIntent": "pi_xxx", "chargeId": "ch_xxx", "escrowStage": "authorized"}' },
          { name: 'isUS', type: 'select', required: false, options: ['false', 'true'] }
        ]
      },
      {
        name: 'Get Invoice by Payment Intent',
        endpoint: '/api/stripe/invoice/by-payment-intent/{paymentIntentId}',
        method: 'GET',
        description: 'Retrieve invoice associated with a payment intent ID',
        fields: [
          { name: 'paymentIntentId', type: 'text', required: true, placeholder: 'pi_3RjhR4E85bVPMZKN0dIEcyFL' },
          { name: 'isUS', type: 'select', required: false, options: ['false', 'true'] }
        ]
      },
      {
        name: 'Create Escrow Invoice',
        endpoint: '/api/stripe/invoice/escrow',
        method: 'POST',
        description: 'Create comprehensive escrow invoice with all payment details',
        fields: [
          { name: 'customerId', type: 'text', required: true, placeholder: 'cus_Sf4EqJYgJfDN1g' },
          { name: 'paymentIntentId', type: 'text', required: true, placeholder: 'pi_3RjhR4E85bVPMZKN0dIEcyFL' },
          { name: 'chargeId', type: 'text', required: false, placeholder: 'ch_3RjhR4E85bVPMZKN0LHt2Xha' },
          { name: 'transactionId', type: 'text', required: false, placeholder: 'EmNgsYGATgJ0pOo4JC25' },
          { name: 'orderId', type: 'text', required: true, placeholder: 'order_21' },
          { name: 'amount', type: 'number', required: true, placeholder: '123412' },
          { name: 'currency', type: 'text', required: false, placeholder: 'usd' },
          { name: 'productName', type: 'text', required: true, placeholder: 'Service Order' },
          { name: 'sellerAccountId', type: 'text', required: false, placeholder: 'acct_...' },
          { name: 'escrowStage', type: 'select', required: false, options: ['authorized', 'accept', 'delivered', 'completed'] },
          { name: 'isUS', type: 'select', required: false, options: ['false', 'true'] }
        ]
      },
      {
        name: '🎯 Generate Invoice from Transaction',
        endpoint: '/api/invoice/generate-from-transaction',
        method: 'POST',
        description: 'Auto-fetch payment intent, charge ID, and generate complete invoice with money breakdown',
        fields: [
          { name: 'transactionId', type: 'text', required: true, placeholder: 'EmNgsYGATgJ0pOo4JC25' },
          { name: 'includeChargeDetails', type: 'select', required: false, options: ['true', 'false'] },
          { name: 'isUS', type: 'select', required: false, options: ['false', 'true'] }
        ]
      }
    ],
    connect: [
      {
        name: 'Connect Onboard',
        endpoint: '/api/connect/onboard',
        method: 'POST',
        description: 'Create Express account and onboarding link',
        fields: []
      }
    ]
  };

  const renderField = (endpoint: string, field: ApiField) => {
    const value = formData[endpoint]?.[field.name] || '';

    if (field.type === 'select') {
      return (
        <select
          value={value}
          onChange={(e) => handleInputChange(endpoint, field.name, e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">Select...</option>
          {field.options?.map(option => (
            <option key={option} value={option}>{option}</option>
          ))}
        </select>
      );
    }

    if (field.type === 'textarea') {
      return (
        <textarea
          value={value}
          onChange={(e) => handleInputChange(endpoint, field.name, e.target.value)}
          placeholder={field.placeholder}
          className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          rows={3}
        />
      );
    }

    return (
      <input
        type={field.type}
        value={value}
        onChange={(e) => handleInputChange(endpoint, field.name, e.target.value)}
        placeholder={field.placeholder}
        className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
      />
    );
  };

  // Function to extract and display key information from API responses
  const renderKeyInformation = (result: ApiResponse) => {
    const keyInfo: Array<{label: string, value: string, type: 'success' | 'info' | 'warning'}> = [];

    // Extract Transaction ID
    if (result.transactionId) {
      keyInfo.push({label: 'Transaction ID', value: result.transactionId, type: 'success'});
    }

    // Extract Charge ID from various possible locations
    if (result.chargeId) {
      keyInfo.push({label: 'Charge ID', value: result.chargeId, type: 'success'});
    }
    if (result.primaryChargeId) {
      keyInfo.push({label: 'Primary Charge ID', value: result.primaryChargeId, type: 'success'});
    }
    if (result.autoFetchedChargeId) {
      keyInfo.push({label: '🔍 Auto-Fetched Charge ID', value: result.autoFetchedChargeId, type: 'success'});
    }
    if (result.summary?.chargeId) {
      keyInfo.push({label: 'Charge ID (Summary)', value: result.summary.chargeId, type: 'success'});
    }
    // Direct Stripe charge object
    if (result.id && result.object === 'charge') {
      keyInfo.push({label: '💳 Stripe Charge ID', value: result.id, type: 'success'});
    }
    // Charge from charges array
    if (result.charges?.data?.length > 0) {
      result.charges.data.forEach((charge: any, index: number) => {
        keyInfo.push({label: `Charge ID #${index + 1}`, value: charge.id, type: 'success'});
      });
    }
    // Latest charge from payment intent
    if (result.latest_charge) {
      keyInfo.push({label: 'Latest Charge ID', value: result.latest_charge, type: 'success'});
    }

    // Extract Session ID
    if (result.sessionId) {
      keyInfo.push({label: 'Session ID', value: result.sessionId, type: 'info'});
    }
    if (result.session?.id) {
      keyInfo.push({label: 'Stripe Session ID', value: result.session.id, type: 'info'});
    }

    // Extract Payment Intent ID
    if (result.paymentIntentId) {
      keyInfo.push({label: 'Payment Intent ID', value: result.paymentIntentId, type: 'info'});
    }
    if (result.paymentIntent?.id) {
      keyInfo.push({label: 'Payment Intent ID', value: result.paymentIntent.id, type: 'info'});
    }
    // Direct from Stripe charge object
    if (result.payment_intent && typeof result.payment_intent === 'string') {
      keyInfo.push({label: 'Payment Intent ID (from charge)', value: result.payment_intent, type: 'info'});
    }
    // Payment intent object
    if (result.id && result.object === 'payment_intent') {
      keyInfo.push({label: '💳 Payment Intent ID', value: result.id, type: 'info'});
    }

    // Extract Transfer ID
    if (result.transferId) {
      keyInfo.push({label: 'Transfer ID', value: result.transferId, type: 'success'});
    }

    // Extract Order ID
    if (result.orderId) {
      keyInfo.push({label: 'Order ID', value: result.orderId, type: 'info'});
    }

    // Extract Amount Information
    if (result.amount) {
      const amountDisplay = typeof result.amount === 'number'
        ? `$${(result.amount / 100).toFixed(2)}`
        : result.amount;
      keyInfo.push({label: 'Amount', value: amountDisplay, type: 'info'});
    }
    if (result.summary?.amountInMajorUnit) {
      keyInfo.push({label: 'Amount', value: `$${result.summary.amountInMajorUnit} ${result.summary.currency || ''}`, type: 'info'});
    }
    // Stripe charge amounts
    if (result.amount_captured && result.object === 'charge') {
      keyInfo.push({label: 'Amount Captured', value: `$${(result.amount_captured / 100).toFixed(2)} ${result.currency?.toUpperCase()}`, type: 'info'});
    }
    if (result.amount_capturable && result.object === 'payment_intent') {
      keyInfo.push({label: 'Amount Capturable', value: `$${(result.amount_capturable / 100).toFixed(2)} ${result.currency?.toUpperCase()}`, type: 'warning'});
    }

    // Extract Status Information
    if (result.status) {
      const statusType = ['succeeded', 'paid', 'completed'].includes(result.status) ? 'success' :
                        ['requires_capture', 'authorized', 'pending'].includes(result.status) ? 'warning' : 'info';
      keyInfo.push({label: 'Status', value: result.status, type: statusType});
    }
    if (result.summary?.status) {
      keyInfo.push({label: 'Charge Status', value: result.summary.status, type: result.summary.status === 'succeeded' ? 'success' : 'warning'});
    }
    // Stripe charge specific statuses
    if (result.captured !== undefined && result.object === 'charge') {
      keyInfo.push({label: 'Captured', value: result.captured ? 'Yes' : 'No', type: result.captured ? 'success' : 'warning'});
    }
    if (result.paid !== undefined && result.object === 'charge') {
      keyInfo.push({label: 'Paid', value: result.paid ? 'Yes' : 'No', type: result.paid ? 'success' : 'warning'});
    }

    // Extract URL (for checkout)
    if (result.url) {
      keyInfo.push({label: 'Checkout URL', value: result.url, type: 'info'});
    }

    if (keyInfo.length === 0) return null;

    return (
      <div className="p-4 bg-gradient-to-r from-blue-50 to-green-50 rounded-md border-2 border-blue-200">
        <h4 className="text-lg font-bold text-gray-900 mb-3 flex items-center">
          <svg className="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          Key Information
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {keyInfo.map((info, index) => (
            <div key={index} className="bg-white rounded-lg p-3 border border-gray-200 shadow-sm">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-600">{info.label}</label>
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                  info.type === 'success' ? 'bg-green-100 text-green-800' :
                  info.type === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-blue-100 text-blue-800'
                }`}>
                  {info.type === 'success' ? '✓' : info.type === 'warning' ? '⚠' : 'ℹ'}
                </span>
              </div>
              <div className="mt-1">
                {info.label.includes('URL') ? (
                  <a
                    href={info.value}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm font-mono text-blue-600 hover:text-blue-800 underline break-all"
                  >
                    {info.value}
                  </a>
                ) : (
                  <div className="flex items-center space-x-2">
                    <p className="text-sm font-mono text-gray-900 bg-gray-100 px-2 py-1 rounded break-all flex-1">
                      {info.value}
                    </p>
                    <button
                      onClick={(e) => {
                        navigator.clipboard.writeText(info.value);
                        // Simple feedback - you could enhance this with a toast
                        const btn = e.target as HTMLButtonElement;
                        const originalText = btn.textContent;
                        btn.textContent = '✓';
                        setTimeout(() => {
                          btn.textContent = originalText;
                        }, 1000);
                      }}
                      className="text-xs bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded transition-colors"
                      title="Copy to clipboard"
                    >
                      📋
                    </button>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Quick copy section for commonly used IDs
  const renderQuickCopySection = (result: ApiResponse) => {
    const ids = [];

    // Collect all IDs
    if (result.transactionId) ids.push({label: 'Transaction ID', value: result.transactionId});
    if (result.chargeId) ids.push({label: 'Charge ID', value: result.chargeId});
    if (result.primaryChargeId) ids.push({label: 'Primary Charge ID', value: result.primaryChargeId});
    if (result.autoFetchedChargeId) ids.push({label: 'Auto-Fetched Charge ID', value: result.autoFetchedChargeId});
    if (result.id && result.object === 'charge') ids.push({label: 'Stripe Charge ID', value: result.id});
    if (result.latest_charge) ids.push({label: 'Latest Charge ID', value: result.latest_charge});
    if (result.orderId) ids.push({label: 'Order ID', value: result.orderId});
    if (result.paymentIntentId) ids.push({label: 'Payment Intent ID', value: result.paymentIntentId});
    if (result.id && result.object === 'payment_intent') ids.push({label: 'Payment Intent ID', value: result.id});
    if (result.payment_intent && typeof result.payment_intent === 'string') ids.push({label: 'Payment Intent ID', value: result.payment_intent});
    if (result.sessionId) ids.push({label: 'Session ID', value: result.sessionId});
    if (result.session?.id) ids.push({label: 'Stripe Session ID', value: result.session.id});
    if (result.transferId) ids.push({label: 'Transfer ID', value: result.transferId});

    if (ids.length === 0) return null;

    return (
      <div className="p-4 bg-yellow-50 border-2 border-yellow-200 rounded-md">
        <h4 className="text-md font-bold text-gray-900 mb-3 flex items-center">
          <svg className="w-5 h-5 mr-2 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
          Quick Copy IDs
        </h4>
        <div className="space-y-2">
          {ids.map((id, index) => (
            <div key={index} className="flex items-center justify-between bg-white p-2 rounded border">
              <div className="flex-1">
                <span className="text-sm font-medium text-gray-700">{id.label}:</span>
                <span className="ml-2 text-sm font-mono text-gray-900">{id.value}</span>
              </div>
              <button
                onClick={(e) => {
                  navigator.clipboard.writeText(id.value);
                  // Simple feedback
                  const btn = e.target as HTMLButtonElement;
                  const originalText = btn.textContent;
                  btn.textContent = '✓ Copied!';
                  btn.className = btn.className.replace('bg-blue-500', 'bg-green-500');
                  setTimeout(() => {
                    btn.textContent = originalText;
                    btn.className = btn.className.replace('bg-green-500', 'bg-blue-500');
                  }, 1500);
                }}
                className="ml-2 bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-xs transition-colors"
              >
                Copy
              </button>
            </div>
          ))}
        </div>
        <div className="mt-3 text-xs text-gray-600">
          💡 <strong>Tip:</strong> Use these IDs in other API calls. Transaction ID + Charge ID are commonly needed for escrow operations.
        </div>
      </div>
    );
  };

  const renderApiEndpoint = (apiEndpoint: ApiEndpoint, index: number) => {
    const result = results[apiEndpoint.endpoint];
    const isLoading = loading === apiEndpoint.endpoint;

    return (
      <div key={`${apiEndpoint.endpoint}-${apiEndpoint.name}-${index}`} className="bg-white rounded-lg shadow-md p-6 mb-4">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{apiEndpoint.name}</h3>
            <p className="text-sm text-gray-600">{apiEndpoint.description}</p>
            <div className="flex items-center mt-2">
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {apiEndpoint.method}
              </span>
              <span className="ml-2 text-sm text-gray-500 font-mono">{apiEndpoint.endpoint}</span>
            </div>
          </div>
        </div>

        {apiEndpoint.fields.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            {apiEndpoint.fields.map(field => (
              <div key={field.name}>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {field.name}
                  {field.required && <span className="text-red-500 ml-1">*</span>}
                </label>
                {renderField(apiEndpoint.endpoint, field)}
              </div>
            ))}
          </div>
        )}

        <div className="flex items-center justify-between">
          <button
            onClick={() => testApi(apiEndpoint)}
            disabled={isLoading}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-300 text-white px-4 py-2 rounded-md font-medium transition-colors"
          >
            {isLoading ? 'Testing...' : 'Test API'}
          </button>
        </div>

        {result && (
          <div className="mt-4 space-y-4">
            {/* Key Information Display */}
            {renderKeyInformation(result)}

            {/* Quick Copy Section for IDs */}
            {renderQuickCopySection(result)}

            {/* Full Response */}
            <div className="p-4 bg-gray-50 rounded-md">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Full Response:</h4>
              <pre className="text-xs text-gray-700 overflow-x-auto">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          </div>
        )}
      </div>
    );
  };

  const QuickCheckout = () => {
    const [user, setUser] = useState<any>(null);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
      const checkAuth = async () => {
        try {
          const { auth } = await initFirebase();
          const currentUser = auth.currentUser;
          if (currentUser) {
            setUser(currentUser);
          } else {
            // Try to get user from localStorage
            const storedUser = localStorage.getItem('user');
            if (storedUser) {
              setUser(JSON.parse(storedUser));
            }
          }
        } catch (error) {
          console.error('Error checking auth:', error);
        }
      };

      checkAuth();
    }, []);

    const handleQuickCheckout = async () => {
      if (!user) {
        alert('Please log in to make a payment');
        return;
      }

      setLoading(true);
      try {
        const res = await fetch('/api/checkout', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            userId: user.uid,
            userEmail: user.email,
            amount: 1000, // $10
            productName: 'Demo Product',
            productDescription: 'A demo product for testing payments'
          }),
        });

        const data = await res.json();
        if (data.url) {
          window.location.href = data.url;
        } else {
          alert('Error creating checkout session: ' + (data.error || 'Unknown error'));
        }
      } catch (error) {
        console.error('Checkout error:', error);
        alert('Error creating checkout session');
      } finally {
        setLoading(false);
      }
    };

    return (
      <div className="space-y-4">
        {user ? (
          <div className="bg-blue-50 p-4 rounded-md">
            <p className="text-sm text-blue-800 mb-2">
              Logged in as: <span className="font-medium">{user.email}</span>
            </p>
            <p className="text-xs text-blue-600">
              User ID: {user.uid}
            </p>
          </div>
        ) : (
          <div className="bg-yellow-50 p-4 rounded-md">
            <p className="text-sm text-yellow-800">
              Please log in to make a payment with your account details.
            </p>
          </div>
        )}

        <button
          onClick={handleQuickCheckout}
          disabled={loading || !user}
          className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-md font-medium transition-colors w-full"
        >
          {loading ? 'Creating Checkout...' : 'Pay $10 with Logged-in Account'}
        </button>
      </div>
    );
  };

  const categories = Object.keys(apiEndpoints);

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Stripe API Tester</h1>
              <p className="text-gray-600">Test all your Stripe API endpoints with dynamic forms</p>
            </div>
            <div className="mt-4 sm:mt-0">
              {/* <button
                onClick={() => window.location.href = '/transactions'}
                className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md font-medium"
              >
                View Transaction History
              </button> */}
            </div>
          </div>

          {/* ID Helper Section - Coming Soon */}
        </div>

        {/* Category Navigation */}
        <div className="mb-6">
          <div className="flex flex-wrap gap-2">
            {categories.map(category => (
              <button
                key={category}
                onClick={() => setActiveCategory(category)}
                className={`px-4 py-2 rounded-md font-medium transition-colors ${
                  activeCategory === category
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                }`}
              >
                {category.charAt(0).toUpperCase() + category.slice(1)}
                <span className="ml-2 text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full">
                  {apiEndpoints[category].length}
                </span>
              </button>
            ))}
          </div>
        </div>

        {/* API Endpoints */}
        <div className="space-y-4">
          {apiEndpoints[activeCategory]?.map((endpoint, index) => renderApiEndpoint(endpoint, index))}
        </div>

        {/* Quick Checkout */}
        <div className="mt-12 p-6 bg-white rounded-lg shadow-md">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Checkout</h2>
          <div className="space-y-4">
            <QuickCheckout />
          </div>
        </div>
      </div>
    </div>
  );
}
