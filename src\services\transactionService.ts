import { initFirebase } from "../../firebaseConfig";
import { collection, doc, setDoc, getDoc, updateDoc, query, where, orderBy, limit, getDocs, Timestamp } from "firebase/firestore";

export interface Transaction {
  id: string;
  userId: string;
  userEmail: string;
  stripeSessionId?: string;
  stripePaymentIntentId?: string;
  stripeCustomerId?: string;
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  productName: string;
  productDescription?: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;

  // Escrow-specific fields
  isEscrow?: boolean;
  sellerId?: string;
  sellerEmail?: string;
  sellerStripeAccountId?: string;
  orderId?: string;

  // Payment breakdown
  subtotal?: number;
  transactionFee?: number; // 4% fee
  platformCommission?: number; // 16% commission
  sellerAmount?: number; // Amount that goes to seller (84% of subtotal)

  // Escrow stages
  escrowStages?: EscrowStage[];
  currentStage?: 'pending' | 'accepted' | 'delivered' | 'completed';
}

export interface EscrowStage {
  stage: 'accept' | 'delivered' | 'completed';
  percentage: number; // 10, 10, 80
  amount: number;
  status: 'pending' | 'released' | 'failed';
  releasedAt?: Date;
  stripeTransferId?: string;
  orderStatus?: string; // The order status that triggers this release
}

export const createTransaction = async (transactionData: Omit<Transaction, 'id' | 'createdAt' | 'updatedAt'>): Promise<{ success: boolean; transactionId?: string; error?: string }> => {
  try {
    const { db } = await initFirebase();
    const transactionsCollection = collection(db, "transactions");

    // Generate a unique transaction ID
    const transactionRef = doc(transactionsCollection);
    const transactionId = transactionRef.id;

    const transaction: Transaction = {
      ...transactionData,
      id: transactionId,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    await setDoc(transactionRef, {
      ...transaction,
      createdAt: Timestamp.fromDate(transaction.createdAt),
      updatedAt: Timestamp.fromDate(transaction.updatedAt),
      completedAt: transaction.completedAt ? Timestamp.fromDate(transaction.completedAt) : null,
      escrowStages: transaction.escrowStages || null,
    });

    return { success: true, transactionId };
  } catch (error) {
    console.error("Error creating transaction:", error);
    return { success: false, error: "Failed to create transaction" };
  }
};

export const createEscrowTransaction = async (escrowData: {
  userId: string;
  userEmail: string;
  sellerId: string;
  sellerEmail: string;
  sellerStripeAccountId: string;
  orderId: string;
  amount: number; // Total order amount ($104 in your example)
  currency: string;
  productName: string;
  productDescription?: string;
  stripeSessionId?: string;
  stripeCustomerId?: string;
  metadata?: Record<string, any>;
}): Promise<{ success: boolean; transactionId?: string; error?: string }> => {
  try {
    console.log('Creating escrow transaction with data:', {
      userId: escrowData.userId,
      sellerId: escrowData.sellerId,
      orderId: escrowData.orderId,
      amount: escrowData.amount,
      currency: escrowData.currency
    });

    // Validate required fields
    if (!escrowData.userId || !escrowData.sellerId || !escrowData.orderId || !escrowData.amount) {
      return { success: false, error: 'Missing required escrow data fields' };
    }

    // Calculate payment breakdown based on your example:
    // Note: escrowData.amount is already in smallest currency unit (cents/pence)
    // For £12.34, amount = 1234 pence
    // For $104.00, amount = 10400 cents

    // Convert to major currency unit for calculations
    const totalInMajorUnit = escrowData.amount / 100; // £12.34 or $104.00
    const subtotalInMajorUnit = totalInMajorUnit / 1.04; // Remove 4% to get subtotal
    const transactionFeeInMajorUnit = totalInMajorUnit - subtotalInMajorUnit; // 4% fee
    const platformCommissionInMajorUnit = subtotalInMajorUnit * 0.16; // 16% commission
    const sellerAmountInMajorUnit = subtotalInMajorUnit - platformCommissionInMajorUnit; // 84% goes to seller

    // Convert back to smallest currency unit for storage
    const subtotal = Math.round(subtotalInMajorUnit * 100);
    const transactionFee = Math.round(transactionFeeInMajorUnit * 100);
    const platformCommission = Math.round(platformCommissionInMajorUnit * 100);
    const sellerAmount = Math.round(sellerAmountInMajorUnit * 100);

    console.log('Payment breakdown:', {
      total: escrowData.amount,
      totalInMajorUnit,
      subtotal,
      subtotalInMajorUnit,
      transactionFee,
      transactionFeeInMajorUnit,
      platformCommission,
      platformCommissionInMajorUnit,
      sellerAmount,
      sellerAmountInMajorUnit,
      currency: escrowData.currency
    });

    // Create escrow stages: 10%, 10%, 80% of seller amount
    // Calculate stage amounts in smallest currency unit (cents/pence)
    const stage1Amount = Math.round(sellerAmount * 0.10); // 10%
    const stage2Amount = Math.round(sellerAmount * 0.10); // 10%
    const stage3Amount = sellerAmount - stage1Amount - stage2Amount; // Remaining 80% (ensures total adds up)

    const escrowStages: EscrowStage[] = [
      {
        stage: 'accept',
        percentage: 10,
        amount: stage1Amount,
        status: 'pending',
        orderStatus: 'accept'
      },
      {
        stage: 'delivered',
        percentage: 10,
        amount: stage2Amount,
        status: 'pending',
        orderStatus: 'delivered'
      },
      {
        stage: 'completed',
        percentage: 80,
        amount: stage3Amount,
        status: 'pending',
        orderStatus: 'completed'
      }
    ];

    console.log('Escrow stages:', escrowStages.map(stage => ({
      stage: stage.stage,
      percentage: stage.percentage,
      amount: stage.amount,
      amountInMajorUnit: stage.amount / 100
    })));

    const transactionData: Omit<Transaction, 'id' | 'createdAt' | 'updatedAt'> = {
      ...escrowData,
      isEscrow: true,
      status: 'pending',
      currentStage: 'pending',
      subtotal,
      transactionFee,
      platformCommission,
      sellerAmount,
      escrowStages
    };

    console.log('Creating transaction with escrow data...');
    const result = await createTransaction(transactionData);

    if (result.success) {
      console.log('Escrow transaction created successfully:', result.transactionId);
    } else {
      console.error('Failed to create escrow transaction:', result.error);
    }

    return result;
  } catch (error) {
    console.error("Error creating escrow transaction:", error);
    const errorMessage = error instanceof Error ? error.message : "Unknown error";
    return { success: false, error: `Failed to create escrow transaction: ${errorMessage}` };
  }
};

export const updateTransaction = async (
  transactionId: string, 
  updates: Partial<Omit<Transaction, 'id' | 'createdAt'>>
): Promise<{ success: boolean; error?: string }> => {
  try {
    const { db } = await initFirebase();
    const transactionRef = doc(db, "transactions", transactionId);
    
    const updateData: any = {
      ...updates,
      updatedAt: Timestamp.fromDate(new Date()),
    };
    
    // Convert Date objects to Timestamps
    if (updates.completedAt) {
      updateData.completedAt = Timestamp.fromDate(updates.completedAt);
    }
    
    await updateDoc(transactionRef, updateData);
    
    return { success: true };
  } catch (error) {
    console.error("Error updating transaction:", error);
    return { success: false, error: "Failed to update transaction" };
  }
};

export const getTransaction = async (transactionId: string): Promise<{ success: boolean; transaction?: Transaction; error?: string }> => {
  try {
    const { db } = await initFirebase();
    const transactionRef = doc(db, "transactions", transactionId);
    const transactionSnap = await getDoc(transactionRef);
    
    if (transactionSnap.exists()) {
      const data = transactionSnap.data();
      const transaction: Transaction = {
        ...data,
        createdAt: data.createdAt.toDate(),
        updatedAt: data.updatedAt.toDate(),
        completedAt: data.completedAt ? data.completedAt.toDate() : undefined,
      } as Transaction;
      
      return { success: true, transaction };
    } else {
      return { success: false, error: "Transaction not found" };
    }
  } catch (error) {
    console.error("Error getting transaction:", error);
    return { success: false, error: "Failed to get transaction" };
  }
};

export const getTransactionByStripeSessionId = async (sessionId: string): Promise<{ success: boolean; transaction?: Transaction; error?: string }> => {
  try {
    const { db } = await initFirebase();
    const transactionsCollection = collection(db, "transactions");
    const q = query(
      transactionsCollection,
      where("stripeSessionId", "==", sessionId),
      limit(1)
    );
    
    const querySnapshot = await getDocs(q);
    
    if (!querySnapshot.empty) {
      const doc = querySnapshot.docs[0];
      const data = doc.data();
      const transaction: Transaction = {
        ...data,
        createdAt: data.createdAt.toDate(),
        updatedAt: data.updatedAt.toDate(),
        completedAt: data.completedAt ? data.completedAt.toDate() : undefined,
      } as Transaction;
      
      return { success: true, transaction };
    } else {
      return { success: false, error: "Transaction not found" };
    }
  } catch (error) {
    console.error("Error getting transaction by session ID:", error);
    return { success: false, error: "Failed to get transaction" };
  }
};

export const getUserTransactions = async (
  userId: string,
  limitCount: number = 10
): Promise<{ success: boolean; transactions?: Transaction[]; error?: string }> => {
  try {
    const { db } = await initFirebase();
    const transactionsCollection = collection(db, "transactions");
    const q = query(
      transactionsCollection,
      where("userId", "==", userId),
      orderBy("createdAt", "desc"),
      limit(limitCount)
    );

    const querySnapshot = await getDocs(q);
    const transactions: Transaction[] = [];

    querySnapshot.forEach((doc) => {
      const data = doc.data();
      transactions.push({
        ...data,
        createdAt: data.createdAt.toDate(),
        updatedAt: data.updatedAt.toDate(),
        completedAt: data.completedAt ? data.completedAt.toDate() : undefined,
      } as Transaction);
    });

    return { success: true, transactions };
  } catch (error) {
    console.error("Error getting user transactions:", error);
    return { success: false, error: "Failed to get transactions" };
  }
};

export const findDuplicateTransactions = async (
  userId: string
): Promise<{ success: boolean; duplicates?: Transaction[]; error?: string }> => {
  try {
    const { db } = await initFirebase();
    const transactionsCollection = collection(db, "transactions");
    const q = query(
      transactionsCollection,
      where("userId", "==", userId),
      orderBy("createdAt", "desc")
    );

    const querySnapshot = await getDocs(q);
    const transactions: Transaction[] = [];
    const sessionIdMap = new Map<string, Transaction[]>();

    querySnapshot.forEach((doc) => {
      const data = doc.data();
      const transaction = {
        ...data,
        createdAt: data.createdAt.toDate(),
        updatedAt: data.updatedAt.toDate(),
        completedAt: data.completedAt ? data.completedAt.toDate() : undefined,
      } as Transaction;

      transactions.push(transaction);

      // Group by session ID to find duplicates
      if (transaction.stripeSessionId) {
        if (!sessionIdMap.has(transaction.stripeSessionId)) {
          sessionIdMap.set(transaction.stripeSessionId, []);
        }
        sessionIdMap.get(transaction.stripeSessionId)!.push(transaction);
      }
    });

    // Find duplicates (sessions with more than one transaction)
    const duplicates: Transaction[] = [];
    sessionIdMap.forEach((transactionGroup) => {
      if (transactionGroup.length > 1) {
        // Keep the first one (oldest), mark others as duplicates
        duplicates.push(...transactionGroup.slice(1));
      }
    });

    return { success: true, duplicates };
  } catch (error) {
    console.error("Error finding duplicate transactions:", error);
    return { success: false, error: "Failed to find duplicates" };
  }
};

export const releaseEscrowStage = async (
  transactionId: string,
  stage: 'accept' | 'delivered' | 'completed',
  stripeTransferId?: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    const { db } = await initFirebase();
    const transactionRef = doc(db, "transactions", transactionId);
    const transactionSnap = await getDoc(transactionRef);

    if (!transactionSnap.exists()) {
      return { success: false, error: "Transaction not found" };
    }

    const transaction = transactionSnap.data() as Transaction;

    if (!transaction.isEscrow || !transaction.escrowStages) {
      return { success: false, error: "Not an escrow transaction" };
    }

    // Find and update the specific stage
    const updatedStages = transaction.escrowStages.map(escrowStage => {
      if (escrowStage.stage === stage) {
        return {
          ...escrowStage,
          status: 'released' as const,
          releasedAt: new Date(),
          stripeTransferId
        };
      }
      return escrowStage;
    });

    // Update current stage
    let currentStage = transaction.currentStage;
    if (stage === 'accept') currentStage = 'accepted';
    else if (stage === 'delivered') currentStage = 'delivered';
    else if (stage === 'completed') currentStage = 'completed';

    await updateDoc(transactionRef, {
      escrowStages: updatedStages,
      currentStage,
      updatedAt: Timestamp.fromDate(new Date())
    });

    return { success: true };
  } catch (error) {
    console.error("Error releasing escrow stage:", error);
    return { success: false, error: "Failed to release escrow stage" };
  }
};

export const getEscrowTransactionByOrderId = async (orderId: string): Promise<{ success: boolean; transaction?: Transaction; error?: string }> => {
  try {
    const { db } = await initFirebase();
    const transactionsCollection = collection(db, "transactions");
    const q = query(
      transactionsCollection,
      where("orderId", "==", orderId),
      where("isEscrow", "==", true)
    );

    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      return { success: false, error: "Escrow transaction not found for this order" };
    }

    const doc = querySnapshot.docs[0];
    const data = doc.data();
    const transaction: Transaction = {
      ...data,
      createdAt: data.createdAt.toDate(),
      updatedAt: data.updatedAt.toDate(),
      completedAt: data.completedAt ? data.completedAt.toDate() : undefined,
      escrowStages: data.escrowStages?.map((stage: any) => ({
        ...stage,
        releasedAt: stage.releasedAt ? stage.releasedAt.toDate() : undefined
      }))
    } as Transaction;

    return { success: true, transaction };
  } catch (error) {
    console.error("Error getting escrow transaction by order ID:", error);
    return { success: false, error: "Failed to get escrow transaction" };
  }
};
