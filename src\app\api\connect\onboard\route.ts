import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { getUserIdFromRequest } from '@/lib/auth/serverAuth';
import { doc, setDoc } from 'firebase/firestore';
import { initFirebase } from '../../../../../firebaseConfig';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string);

export async function POST(req: NextRequest) {
  try {
    // Get user ID from request (body, auth header, or query param)
    let userId = await getUserIdFromRequest(req);

    // If no user ID found, try to get it from request body
    if (!userId) {
      try {
        const body = await req.json();
        userId = body.userId;
      } catch (error) {
        // Body might not be JSON
      }
    }

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required. Please provide userId in request body or authenticate with <PERSON><PERSON> token.' },
        { status: 400 }
      );
    }

    console.log('Creating Stripe account for user:', userId);

    // 1. Create a new Express account for the seller
    const account = await stripe.accounts.create({ type: 'express' });

    // 2. Save account to Firebase immediately
    try {
      const { db } = await initFirebase();
      const sellerRef = doc(db, 'sellers', userId);
      await setDoc(sellerRef, {
        stripeAccountId: account.id,
        createdAt: new Date().toISOString(),
        onboardingComplete: false
      }, { merge: true });

      console.log('Saved Stripe account to Firebase:', { userId, accountId: account.id });
    } catch (firebaseError) {
      console.error('Error saving to Firebase:', firebaseError);
      // Continue with onboarding even if Firebase save fails
    }

    // 3. Create an onboarding link
    const origin = req.headers.get('origin') || 'http://localhost:3000';
    const accountLink = await stripe.accountLinks.create({
      account: account.id,
      refresh_url: `${origin}/payment?userId=${userId}`,
      return_url: `${origin}/payment?account=${account.id}&userId=${userId}`,
      type: 'account_onboarding',
    });

    return NextResponse.json({
      url: accountLink.url,
      accountId: account.id,
      userId
    });
  } catch (error) {
    console.error('Error creating Stripe account:', error);
    return NextResponse.json(
      { error: 'Failed to create account' },
      { status: 500 }
    );
  }
}
