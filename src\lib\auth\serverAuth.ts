import { NextRequest } from 'next/server';

// Mock Firebase Admin for development/testing
// Remove this file and implement proper Firebase Admin when ready for production

export interface AuthenticatedUser {
  uid: string;
  email?: string;
  emailVerified: boolean;
}

/**
 * Extract user ID from Authorization header or request body
 * Mock implementation for development - replace with real Firebase Admin auth
 */
export async function getCurrentUser(request: NextRequest): Promise<AuthenticatedUser | null> {
  try {
    // Method 1: Check Authorization header (mock)
    const authHeader = request.headers.get('authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      // Mock: return a test user
      return {
        uid: 'user_123',
        email: '<EMAIL>',
        emailVerified: true,
      };
    }

    // Method 2: Check for user ID in request body (for POST requests)
    if (request.method === 'POST') {
      try {
        const body = await request.json();
        if (body.userId) {
          // Mock: return the provided user ID
          return {
            uid: body.userId,
            email: `${body.userId}@example.com`,
            emailVerified: true,
          };
        }
      } catch (error) {
        // Request body might not be JSON or might be already consumed
      }
    }

    // Method 3: Check query parameters
    const url = new URL(request.url);
    const userIdParam = url.searchParams.get('userId');
    if (userIdParam) {
      // Mock: return the provided user ID
      return {
        uid: userIdParam,
        email: `${userIdParam}@example.com`,
        emailVerified: true,
      };
    }

    // Mock: return default user for development
    console.log('No user found in request, returning default user_123');
    return {
      uid: 'user_123',
      email: '<EMAIL>',
      emailVerified: true,
    };
  } catch (error) {
    console.error('Error getting current user:', error);
    // Even on error, return default for development
    return {
      uid: 'user_123',
      email: '<EMAIL>',
      emailVerified: true,
    };
  }
}

/**
 * Simplified version that just extracts user ID from common sources
 * Mock implementation for development - returns default user ID if none found
 */
export async function getUserIdFromRequest(request: NextRequest): Promise<string | null> {
  try {
    // Check Authorization header (mock)
    const authHeader = request.headers.get('authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      // Mock: return test user ID
      return 'user_123';
    }

    // Check request body for userId
    if (request.method === 'POST') {
      try {
        const body = await request.json();
        if (body.userId) {
          return body.userId;
        }
      } catch (error) {
        // Body might not be JSON
      }
    }

    // Check query parameters
    const url = new URL(request.url);
    const userIdParam = url.searchParams.get('userId');
    if (userIdParam) {
      return userIdParam;
    }

    // Mock: return default user ID for development
    console.log('No user ID found in request, returning default user_123');
    return 'user_123';
  } catch (error) {
    console.error('Error extracting user ID:', error);
    // Even on error, return default for development
    return 'user_123';
  }
}

/**
 * Require authentication - throws error if user not authenticated
 */
export async function requireAuth(request: NextRequest): Promise<AuthenticatedUser> {
  const user = await getCurrentUser(request);
  if (!user) {
    throw new Error('Authentication required');
  }
  return user;
}
