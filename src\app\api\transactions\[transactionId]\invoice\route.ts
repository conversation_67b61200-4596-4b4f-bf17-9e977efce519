import { NextRequest, NextResponse } from 'next/server';
import { getTransaction } from '@/services/transactionService';
import Strip<PERSON> from 'stripe';

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error("STRIPE_SECRET_KEY is not defined");
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ transactionId: string }> }
) {
  try {
    const { transactionId } = await params;

    console.log('🧾 ===== GENERATING INVOICE FOR TRANSACTION =====');
    console.log(`📋 Transaction ID: ${transactionId}`);

    // Step 1: Get transaction from Firebase
    console.log('🔄 Step 1: Fetching transaction from Firebase...');
    const transactionResult = await getTransaction(transactionId);

    if (!transactionResult.success || !transactionResult.transaction) {
      console.error('❌ Transaction not found');
      return NextResponse.json({
        success: false,
        error: 'Transaction not found'
      }, { status: 404 });
    }

    const transaction = transactionResult.transaction;
    console.log('✅ Transaction found:', {
      orderId: transaction.orderId,
      amount: transaction.amount,
      sessionId: transaction.sessionId,
      userEmail: transaction.userEmail
    });

    // Step 2: Get Stripe session and payment intent
    console.log('🔄 Step 2: Fetching Stripe session and payment intent...');
    let paymentIntentId = null;
    let paymentIntent = null;
    let stripeSession = null;
    let latestCharge = null;

    if (transaction.sessionId) {
      try {
        // Get session with expanded payment intent and charges
        stripeSession = await stripe.checkout.sessions.retrieve(transaction.sessionId, {
          expand: ['payment_intent', 'payment_intent.charges.data.payment_method_details']
        });

        paymentIntentId = stripeSession.payment_intent?.id || stripeSession.payment_intent;
        paymentIntent = stripeSession.payment_intent;

        if (paymentIntent && paymentIntent.charges?.data?.length > 0) {
          latestCharge = paymentIntent.charges.data[0];
        }

        console.log('✅ Payment data extracted:', {
          paymentIntentId,
          chargeId: latestCharge?.id,
          paymentStatus: paymentIntent?.status,
          chargeStatus: latestCharge?.status
        });

      } catch (error) {
        console.warn('⚠️ Could not fetch Stripe session:', error);
      }
    }

    // Step 3: Calculate money breakdown
    console.log('🔄 Step 3: Calculating money breakdown...');
    const amount = transaction.amount;
    const currency = transaction.currency || 'usd';

    // Calculate fees and commissions
    const transactionFee = Math.round(amount * 0.04); // 4% transaction fee
    const subtotal = amount - transactionFee;
    const platformCommission = Math.round(amount * 0.16); // 16% platform commission
    const sellerAmount = Math.round(amount * 0.84); // 84% to seller

    // Calculate escrow stages (10% + 10% + 80% of seller amount)
    const escrowStages = {
      accept: {
        percentage: 10,
        amount: Math.round(sellerAmount * 0.10),
        amountFormatted: `$${(Math.round(sellerAmount * 0.10) / 100).toFixed(2)}`
      },
      delivered: {
        percentage: 10,
        amount: Math.round(sellerAmount * 0.10),
        amountFormatted: `$${(Math.round(sellerAmount * 0.10) / 100).toFixed(2)}`
      },
      completed: {
        percentage: 80,
        amount: Math.round(sellerAmount * 0.80),
        amountFormatted: `$${(Math.round(sellerAmount * 0.80) / 100).toFixed(2)}`
      }
    };

    // Step 4: Generate comprehensive invoice
    console.log('🔄 Step 4: Generating invoice...');
    const invoice = {
      // Invoice metadata
      invoiceId: `INV-${transactionId}-${Date.now()}`,
      generatedAt: new Date().toISOString(),
      type: 'escrow_payment',

      // Transaction details
      transactionId,
      orderId: transaction.orderId,
      sessionId: transaction.sessionId,

      // Payment identifiers
      paymentIntentId,
      chargeId: latestCharge?.id || null,
      customerId: stripeSession?.customer || transaction.userId,

      // Customer information
      customer: {
        id: transaction.userId,
        email: transaction.userEmail,
        stripeCustomerId: stripeSession?.customer
      },

      // Product information
      product: {
        name: transaction.productName || 'Service Order',
        description: transaction.productDescription || 'Escrow payment service'
      },

      // Money breakdown
      amounts: {
        // Original amounts
        total: amount,
        totalFormatted: `$${(amount / 100).toFixed(2)}`,
        currency: currency.toUpperCase(),

        // Breakdown
        subtotal: subtotal,
        subtotalFormatted: `$${(subtotal / 100).toFixed(2)}`,
        transactionFee: transactionFee,
        transactionFeeFormatted: `$${(transactionFee / 100).toFixed(2)}`,
        platformCommission: platformCommission,
        platformCommissionFormatted: `$${(platformCommission / 100).toFixed(2)}`,
        sellerAmount: sellerAmount,
        sellerAmountFormatted: `$${(sellerAmount / 100).toFixed(2)}`,

        // Escrow stages
        escrowStages
      },

      // Payment status
      paymentStatus: {
        intent: paymentIntent?.status || 'unknown',
        charge: latestCharge?.status || 'unknown',
        captured: latestCharge?.captured || false,
        amountCaptured: latestCharge?.amount_captured || 0,
        amountCapturedFormatted: `$${((latestCharge?.amount_captured || 0) / 100).toFixed(2)}`,
        amountRefunded: latestCharge?.amount_refunded || 0,
        captureMethod: paymentIntent?.capture_method || 'unknown'
      },

      // Escrow information
      escrow: {
        isEscrow: true,
        totalStages: 3,
        stagesCompleted: 0,
        nextStage: 'accept',
        availableForCapture: paymentIntent?.amount_capturable || 0,
        availableForCaptureFormatted: `$${((paymentIntent?.amount_capturable || 0) / 100).toFixed(2)}`
      },

      // Timestamps
      createdAt: transaction.createdAt,
      paymentCreatedAt: paymentIntent?.created ? new Date(paymentIntent.created * 1000).toISOString() : null,

      // Additional details
      metadata: {
        platform: 'Escrow Payment System',
        version: '1.0',
        generatedBy: 'API'
      }
    };

    console.log('✅ ===== INVOICE GENERATED SUCCESSFULLY =====');
    console.log(`🧾 Invoice ID: ${invoice.invoiceId}`);
    console.log(`💳 Payment Intent: ${invoice.paymentIntentId}`);
    console.log(`🔗 Charge ID: ${invoice.chargeId}`);
    console.log(`💰 Total: ${invoice.amounts.totalFormatted}`);
    console.log(`🏦 Seller Amount: ${invoice.amounts.sellerAmountFormatted}`);
    console.log(`📊 Escrow Stages:`, {
      accept: invoice.amounts.escrowStages.accept.amountFormatted,
      delivered: invoice.amounts.escrowStages.delivered.amountFormatted,
      completed: invoice.amounts.escrowStages.completed.amountFormatted
    });

    return NextResponse.json({
      success: true,
      invoice,
      summary: {
        invoiceId: invoice.invoiceId,
        transactionId,
        paymentIntentId: invoice.paymentIntentId,
        chargeId: invoice.chargeId,
        totalAmount: invoice.amounts.totalFormatted,
        sellerAmount: invoice.amounts.sellerAmountFormatted,
        escrowStages: invoice.amounts.escrowStages,
        paymentStatus: invoice.paymentStatus.intent,
        chargeStatus: invoice.paymentStatus.charge
      }
    });

  } catch (error) {
    console.error('❌ Error generating invoice:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to generate invoice',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
